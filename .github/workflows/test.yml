name: Run Tests

on:
  push:
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Matches pyproject.toml

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: 'recursive' # As per AGENTS.md, vendor/llama.cpp is a submodule

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      run: |
        pipx install poetry

    - name: Install dependencies
      run: |
        poetry install

    - name: Debug CLI import
      run: |
        poetry run python -c "
        import sys
        sys.path.insert(0, 'src')
        try:
            from llama_tune.cli import app
            from typer.testing import CliRunner
            runner = CliRunner()
            result = runner.invoke(app, ['--help'])
            print('CLI help test:')
            print('Exit code:', result.exit_code)
            print('--force in output:', '--force' in result.stdout)
            print('--no-cache in output:', '--no-cache' in result.stdout)
            print('Output length:', len(result.stdout))
            if '--force' not in result.stdout:
                print('ERROR: --force flag missing!')
                print('First 1000 chars:', repr(result.stdout[:1000]))
        except Exception as e:
            print('ERROR:', e)
            import traceback
            traceback.print_exc()
        "

    - name: Run tests
      run: |
        poetry run pytest -v --ignore=vendor

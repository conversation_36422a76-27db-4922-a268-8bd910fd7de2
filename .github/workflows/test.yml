name: Run Tests

on:
  push:
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Matches pyproject.toml

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: 'recursive' # As per AGENTS.md, vendor/llama.cpp is a submodule

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      run: |
        pipx install poetry

    - name: Install dependencies
      run: |
        poetry install

    - name: Debug CLI import
      run: |
        echo "=== Debug CLI Import ==="
        poetry run python -c "
        import sys
        print('Python version:', sys.version)
        print('Python path:', sys.path[:3])

        # Test basic imports
        try:
            import typer
            print('typer version:', typer.__version__)
        except Exception as e:
            print('typer import error:', e)

        try:
            from typing_extensions import Annotated
            print('typing_extensions imported successfully')
        except Exception as e:
            print('typing_extensions import error:', e)

        # Test CLI import
        try:
            from llama_tune.cli import app
            print('CLI imported successfully')

            from typer.testing import CliRunner
            runner = CliRunner()
            result = runner.invoke(app, ['--help'])
            print('=== CLI Help Test ===')
            print('Exit code:', result.exit_code)
            print('Output length:', len(result.stdout))
            print('--force in output:', '--force' in result.stdout)
            print('--no-cache in output:', '--no-cache' in result.stdout)

            if '--force' not in result.stdout:
                print('ERROR: --force flag missing!')
                print('Available options:')
                lines = result.stdout.split('\n')
                for line in lines:
                    if '--' in line and '│' in line:
                        print('  ', line.strip())
        except Exception as e:
            print('CLI import/test error:', e)
            import traceback
            traceback.print_exc()
        "

    - name: Run CLI debug script
      run: |
        poetry run python test_cli_debug.py

    - name: Run tests
      run: |
        poetry run pytest -v --ignore=vendor

name: Run Tests

on:
  push:
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Matches pyproject.toml

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: 'recursive' # As per AGENTS.md, vendor/llama.cpp is a submodule

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      run: |
        pipx install poetry

    - name: Install dependencies
      run: |
        poetry install

    - name: Debug CLI import
      run: |
        echo "=== Debug CLI Import ==="
        poetry run python -c "
        import sys
        print('Python version:', sys.version)
        print('Python path:', sys.path[:3])

        # Test basic imports
        try:
            import typer
            print('typer version:', typer.__version__)
        except Exception as e:
            print('typer import error:', e)

        try:
            from typing_extensions import Annotated
            print('typing_extensions imported successfully')
        except Exception as e:
            print('typing_extensions import error:', e)

        # Test CLI import
        try:
            from llama_tune.cli import app
            print('CLI imported successfully')

            from typer.testing import CliRunner
            runner = CliRunner()
            result = runner.invoke(app, ['--help'])
            print('=== CLI Help Test ===')
            print('Exit code:', result.exit_code)
            print('Output length:', len(result.stdout))
            print('--force in output:', '--force' in result.stdout)
            print('--no-cache in output:', '--no-cache' in result.stdout)

            if '--force' not in result.stdout:
                print('ERROR: --force flag missing!')
                print('Available options:')
                lines = result.stdout.split('\n')
                for line in lines:
                    if '--' in line and '│' in line:
                        print('  ', line.strip())
        except Exception as e:
            print('CLI import/test error:', e)
            import traceback
            traceback.print_exc()
        "

    - name: Run CLI debug script
      run: |
        poetry run python -c "
        import sys
        import os
        sys.path.insert(0, os.path.join(os.getcwd(), 'src'))

        print('=== CLI Debug Test ===')
        print('Python version:', sys.version)
        print('Working directory:', os.getcwd())
        print('Python path:', sys.path[:3])

        print('\n=== Testing imports ===')

        try:
            import typer
            print('✓ typer imported successfully (version:', typer.__version__, ')')
        except Exception as e:
            print('✗ typer import failed:', e)
            sys.exit(1)

        try:
            from typing_extensions import Annotated
            print('✓ typing_extensions imported successfully')
        except Exception as e:
            print('✗ typing_extensions import failed:', e)
            sys.exit(1)

        try:
            from llama_tune.cli import app
            print('✓ llama_tune.cli imported successfully')
        except Exception as e:
            print('✗ llama_tune.cli import failed:', e)
            import traceback
            traceback.print_exc()
            sys.exit(1)

        print('\n=== Testing CLI help ===')
        try:
            from typer.testing import CliRunner
            runner = CliRunner()
            result = runner.invoke(app, ['--help'])

            print('Exit code:', result.exit_code)
            print('Output length:', len(result.stdout))

            has_force = '--force' in result.stdout
            has_no_cache = '--no-cache' in result.stdout
            has_model_path = '--model-path' in result.stdout

            print('--force present:', has_force)
            print('--no-cache present:', has_no_cache)
            print('--model-path present:', has_model_path)

            if not has_force or not has_no_cache:
                print('\n✗ ERROR: Missing cache flags!')
                print('Available options:')
                lines = result.stdout.split('\n')
                for line in lines:
                    if '--' in line and '│' in line:
                        print('  ', line.strip())
                print('\nFirst 500 chars of output:')
                print(repr(result.stdout[:500]))
                sys.exit(1)
            else:
                print('✓ All cache flags present')

        except Exception as e:
            print('✗ CLI help test failed:', e)
            import traceback
            traceback.print_exc()
            sys.exit(1)
        "

    - name: Run tests
      run: |
        poetry run pytest -v --ignore=vendor

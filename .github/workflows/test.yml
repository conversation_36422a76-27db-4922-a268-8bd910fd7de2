name: Run Tests

on:
  push:
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Matches pyproject.toml

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: 'recursive' # As per AGENTS.md, vendor/llama.cpp is a submodule

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      run: |
        pipx install poetry

    - name: Install dependencies
      run: |
        poetry install

    - name: Debug CLI import
      run: |
        echo "=== Debug CLI Import ==="
        poetry run python -c "
        import sys
        print('Python version:', sys.version)
        print('Python path:', sys.path[:3])

        # Test basic imports
        try:
            import typer
            print('typer version:', typer.__version__)
        except Exception as e:
            print('typer import error:', e)

        try:
            from typing_extensions import Annotated
            print('typing_extensions imported successfully')
        except Exception as e:
            print('typing_extensions import error:', e)

        # Test CLI import
        try:
            from llama_tune.cli import app
            print('CLI imported successfully')

            from typer.testing import CliRunner
            runner = CliRunner()
            result = runner.invoke(app, ['--help'])
            print('=== CLI Help Test ===')
            print('Exit code:', result.exit_code)
            print('Output length:', len(result.stdout))
            print('--force in output:', '--force' in result.stdout)
            print('--no-cache in output:', '--no-cache' in result.stdout)

            if '--force' not in result.stdout:
                print('ERROR: --force flag missing!')
                print('Available options:')
                lines = result.stdout.split('\n')
                for line in lines:
                    if '--' in line and '│' in line:
                        print('  ', line.strip())
        except Exception as e:
            print('CLI import/test error:', e)
            import traceback
            traceback.print_exc()
        "

    - name: Run CLI debug script
      run: |
        poetry run python -c "
        import sys
        import os
        sys.path.insert(0, os.path.join(os.getcwd(), 'src'))

        print('=== CLI Debug Test ===')
        print('Python version:', sys.version)
        print('Working directory:', os.getcwd())
        print('Python path:', sys.path[:3])

        print('\n=== Testing imports ===')

        try:
            import typer
            print('✓ typer imported successfully (version:', typer.__version__, ')')
        except Exception as e:
            print('✗ typer import failed:', e)
            sys.exit(1)

        try:
            from typing_extensions import Annotated
            print('✓ typing_extensions imported successfully')
        except Exception as e:
            print('✗ typing_extensions import failed:', e)
            sys.exit(1)

        try:
            from llama_tune.cli import app
            print('✓ llama_tune.cli imported successfully')

            # Debug: Check what commands are registered
            print('Registered commands:', len(app.registered_commands))
            for i, cmd in enumerate(app.registered_commands):
                print(f'  Command {i}: {cmd.name if hasattr(cmd, \"name\") else \"unnamed\"}')
                if hasattr(cmd, 'callback') and hasattr(cmd.callback, '__code__'):
                    print(f'    Parameters: {cmd.callback.__code__.co_varnames[:cmd.callback.__code__.co_argcount]}')

                # Debug: Check command attributes
                print(f'    Command type: {type(cmd)}')
                print(f'    Command attributes: {[attr for attr in dir(cmd) if not attr.startswith(\"_\")]}')

                # Try to inspect the command's parameter info
                if hasattr(cmd, 'params'):
                    print(f'    Command params: {len(cmd.params) if cmd.params else 0}')
                    if cmd.params:
                        for j, param in enumerate(cmd.params[:5]):  # Show first 5 params
                            print(f'      Param {j}: {param.name if hasattr(param, \"name\") else \"unknown\"} - {type(param)}')
                elif hasattr(cmd, 'get_params'):
                    try:
                        import click
                        ctx = click.Context(cmd)
                        params = cmd.get_params(ctx)
                        print(f'    Command get_params: {len(params)}')
                        for j, param in enumerate(params[:5]):  # Show first 5 params
                            print(f'      Param {j}: {param.name if hasattr(param, \"name\") else \"unknown\"} - {type(param)}')
                    except Exception as e:
                        print(f'    Error getting params: {e}')
        except Exception as e:
            print('✗ llama_tune.cli import failed:', e)
            import traceback
            traceback.print_exc()
            sys.exit(1)

        print('\n=== Testing CLI help ===')
        try:
            from typer.testing import CliRunner
            runner = CliRunner()

            # Debug: Try to get the actual click command
            try:
                import click
                click_cmd = typer.main.get_command(app)
                print('Click command type:', type(click_cmd))
                if hasattr(click_cmd, 'params'):
                    print('Click command params:', len(click_cmd.params))
                    for j, param in enumerate(click_cmd.params[:10]):  # Show first 10 params
                        print(f'  Click param {j}: {param.name} - {type(param)}')
            except Exception as e:
                print('Error getting click command:', e)

            result = runner.invoke(app, ['--help'])

            print('Exit code:', result.exit_code)
            print('Output length:', len(result.stdout))

            # Debug: Try invoking with --force to see if it works
            print('\n=== Testing --force flag directly ===')
            try:
                force_result = runner.invoke(app, ['--force'])
                print('--force exit code:', force_result.exit_code)
                print('--force stderr:', repr(force_result.stderr) if force_result.stderr else 'None')
                if force_result.exit_code == 2:  # Click error for unknown option
                    print('--force flag not recognized by click')
                else:
                    print('--force flag recognized by click')
            except Exception as e:
                print('Error testing --force:', e)

            has_force = '--force' in result.stdout
            has_no_cache = '--no-cache' in result.stdout
            has_model_path = '--model-path' in result.stdout

            print('--force present:', has_force)
            print('--no-cache present:', has_no_cache)
            print('--model-path present:', has_model_path)

            if not has_force or not has_no_cache:
                print('\n✗ ERROR: Missing cache flags!')
                print('Available options:')
                lines = result.stdout.split('\n')
                option_lines = []
                for line in lines:
                    if '--' in line and '│' in line:
                        option_lines.append(line.strip())
                        print('  ', line.strip())

                print(f'\nFound {len(option_lines)} option lines')
                print('\nAll lines containing \"--\":')
                for i, line in enumerate(lines):
                    if '--' in line:
                        print(f'  Line {i}: {repr(line)}')

                print('\nFirst 1000 chars of output:')
                print(repr(result.stdout[:1000]))

                print('\nLast 500 chars of output:')
                print(repr(result.stdout[-500:]))
                sys.exit(1)
            else:
                print('✓ All cache flags present')

        except Exception as e:
            print('✗ CLI help test failed:', e)
            import traceback
            traceback.print_exc()
            sys.exit(1)
        "

    - name: Run tests
      run: |
        poetry run pytest -v --ignore=vendor

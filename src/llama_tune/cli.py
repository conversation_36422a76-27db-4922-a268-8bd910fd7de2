import typer
import logging
import sys
import importlib
from typing_extensions import Annotated
from typing import Optional

# Import basic dependencies first
try:
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
    from rich.console import Console
except ImportError as e:
    print(f"Warning: Rich import error: {e}", file=sys.stderr)
    # Create minimal fallbacks
    Progress = None
    SpinnerColumn = None
    TextColumn = None
    BarColumn = None
    TimeElapsedColumn = None
    TimeRemainingColumn = None
    Console = None

# Import application modules with individual error handling
analyzer_get_system_profile = None
analyzer_get_model_profile = None
analyzer_run_feasibility_check = None
get_model_metadata = None
FeasibilityError = Exception
OptimalConfiguration = None
BenchmarkResult = None
ModelProfile = None
generate_output = None
generate_dry_run_plan = None
BenchmarkingEngine = None
ProgressCallback = None

try:
    from llama_tune.analyzer.analyzer import get_system_profile as analyzer_get_system_profile, get_model_profile as analyzer_get_model_profile, run_feasibility_check as analyzer_run_feasibility_check, get_model_metadata, FeasibilityError
except ImportError as e:
    print(f"Warning: Analyzer import error: {e}", file=sys.stderr)

try:
    from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, ModelProfile
except ImportError as e:
    print(f"Warning: Data models import error: {e}", file=sys.stderr)

try:
    from llama_tune.reporting.output_generator import generate_output, generate_dry_run_plan
except ImportError as e:
    print(f"Warning: Output generator import error: {e}", file=sys.stderr)

try:
    from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine, ProgressCallback
except ImportError as e:
    print(f"Warning: Benchmarking engine import error: {e}", file=sys.stderr)

app = typer.Typer()
logger = logging.getLogger(__name__)

@app.command()
def tune(
    model_path: Annotated[Optional[str], typer.Option("--model-path", "-m", help="Path to the GGUF model file.")] = None,
    verbose: Annotated[bool, typer.Option(help="Enable verbose output.")] = False,
    json_output: Annotated[bool, typer.Option("--json", help="Output results as JSON.")] = False,
    interactive: Annotated[bool, typer.Option("--interactive", help="Launch an interactive setup wizard.")] = False,
    benchmark: Annotated[bool, typer.Option("--benchmark", help="Initiate automated benchmarking.")] = False,
    dry_run: Annotated[bool, typer.Option("--dry-run", help="Preview benchmark plan without executing tests.")] = False,
    ctx_size: Annotated[Optional[int], typer.Option("--ctx-size", help="Target context size for benchmarking.")] = None,
    max_vram_gb: Annotated[Optional[float], typer.Option("--max-vram-gb", help="Maximum GPU VRAM to utilize in GB.")] = None,
    llama_server_url: Annotated[Optional[str], typer.Option("--llama-server-url", help="URL of the llama-server to use for benchmarking.")] = None,
    num_runs: Annotated[int, typer.Option("--num-runs", "-n", help="Number of times to run each benchmark configuration for statistical analysis.")] = 3,
    use_case: Annotated[str, typer.Option("--use-case", help="Use case for benchmarking: 'default' or 'multi-user-server'.")] = "default",
    plot: Annotated[bool, typer.Option("--plot", help="Generate an interactive HTML plot of the Pareto front.")] = False,
    force: Annotated[bool, typer.Option("--force", help="Force a new benchmark, bypassing any cached results.")] = False,
    no_cache: Annotated[bool, typer.Option("--no-cache", help="Disable caching of benchmark results.")] = False,
):
    """
    Analyzes system hardware and GGUF model to recommend optimal llama.cpp settings.
    """
    # Check if required imports are available
    if analyzer_get_system_profile is None:
        typer.echo("Error: Required modules could not be imported. Please check your installation.", err=True)
        if json_output:
            typer.echo('{"error": "Required modules could not be imported"}')
        sys.exit(1)

    # Configure logging to go to stderr when JSON mode is active to keep stdout clean
    if json_output:
        logging.basicConfig(level=logging.CRITICAL, stream=sys.stderr)  # Suppress most logging in JSON mode
    else:
        logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)

    # Validate required arguments for benchmark mode
    if benchmark or dry_run:
        if not model_path:
            typer.echo("Error: --model-path is required when using --benchmark or --dry-run.")
            if not json_output:
                print("DEBUG: Exiting due to missing model_path")
            sys.exit(1)
        if not ctx_size:
            typer.echo("Error: --ctx-size is required when using --benchmark or --dry-run.")
            sys.exit(1)

    # Validate that dry-run is only used with benchmark
    if dry_run and not benchmark:
        typer.echo("Error: --dry-run can only be used with --benchmark.")
        sys.exit(1)

    # 1. Detect System Profile
    system_profile = analyzer_get_system_profile()
    logger.info(f"Detected System Profile: {system_profile}")

    # 2. Get Model Profile
    model_profile = None
    if model_path:
        try:
            if not json_output:
                print(f"DEBUG: Calling get_model_profile with {model_path}")
            model_profile = analyzer_get_model_profile(model_path)
            logger.info(f"Detected Model Profile: {model_profile}")
        except FeasibilityError as e:
            typer.echo(f"Error: {e}")
            sys.exit(1)

    # 3. Perform Pre-flight Feasibility Check
    if model_profile and system_profile:
        try:
            analyzer_run_feasibility_check(model_profile, system_profile)
        except FeasibilityError as e:
            typer.echo(f"Error: {e}")
            sys.exit(1)

    if interactive:
        if not json_output:
            typer.echo("Welcome to the Llama-Tune Interactive Setup Wizard!")
            typer.echo("This wizard will guide you through configuring optimal settings for your GGUF model.")
        from llama_tune.wizard.interactive_wizard import InteractiveWizard
        wizard = InteractiveWizard()
        try:
            wizard.start()
        except (KeyboardInterrupt, EOFError):
            if not json_output:
                typer.echo("Wizard interrupted.")
            else:
                typer.echo('{"error": "Wizard interrupted"}')
            sys.exit(1)
        
        optimal_config = wizard.get_optimal_configuration()
        if optimal_config:
            output_mode = "json" if json_output else ("verbose" if verbose else "default")
            final_output = generate_output(optimal_config, output_mode)

            if output_mode == "json":
                # JSON mode: only output the JSON to stdout, no extra text
                typer.echo(final_output)
            elif output_mode == "default":
                # Default mode: only output the command string to stdout
                typer.echo(final_output)
            else:
                # Verbose mode: include additional information
                typer.echo("\n" + final_output)
                typer.echo("Configuration complete!")
        else:
            if not json_output:
                typer.echo("Wizard did not complete successfully. No command generated.")
            else:
                # In JSON mode, output empty JSON object or error JSON
                typer.echo('{"error": "Wizard did not complete successfully"}')
        sys.exit()


    if benchmark:
        if model_path is None:
            typer.echo("Error: --model-path is required when using --benchmark.")
            sys.exit(1)
        if ctx_size is None:
            typer.echo("Error: --ctx-size is required when using --benchmark.")
            sys.exit(1)
        
        # Task 3: Call BenchmarkingEngine.run_benchmark()
        # Determine cache usage: disable if --no-cache or --force is specified
        use_cache = not (no_cache or force)
        benchmarking_engine = BenchmarkingEngine(llama_server_url=llama_server_url, use_cache=use_cache)
        
        console = Console()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            
            console=console,
            transient=True,
        ) as progress:
            overall_task = progress.add_task("[green]Overall Benchmark Progress", total=100)

            def update_progress_callback(phase_name: str, step_description: str, current_step: int, total_steps: int, speed: Optional[float] = None, run_idx: Optional[int] = None, num_runs: Optional[int] = None):
                progress_percentage = (current_step / total_steps) * 100 if total_steps > 0 else 0
                run_info = f" (Run {run_idx}/{num_runs})" if run_idx is not None and num_runs is not None else ""
                speed_info = f" - {speed:.2f} t/s" if speed is not None else ""
                progress.update(overall_task, description=f"[green]{phase_name}: [cyan]{step_description}{run_info}{speed_info}", completed=progress_percentage)
                
            optimal_config, all_benchmark_results = benchmarking_engine.run_benchmark(model_path, ctx_size, use_case, num_runs, update_progress_callback, max_vram_gb, dry_run)

        # Handle dry-run output
        if dry_run:
            if hasattr(optimal_config, 'benchmark_plan') and optimal_config.benchmark_plan:
                dry_run_output = generate_dry_run_plan(optimal_config.benchmark_plan)
                typer.echo(dry_run_output)
            else:
                typer.echo("Error: Dry-run plan could not be generated.")
                sys.exit(1)
            sys.exit(0)

        output_mode = "json" if json_output else ("verbose" if verbose else "default")
        plot_file_path = "pareto_plot.html" if plot else None
        final_output = generate_output(optimal_config, output_mode, all_benchmark_results, plot_file_path)

        if output_mode == "json":
            # JSON mode: only output the JSON to stdout, no extra text
            typer.echo(final_output)
        elif output_mode == "default":
            # Default mode: only output the command string to stdout
            typer.echo(final_output)
        else:
            # Verbose mode: include additional information
            typer.echo("\n" + final_output)
            typer.echo("Benchmarking complete!")
        sys.exit()

    if not interactive and not benchmark:
        # The logic for generating optimal_config for non-interactive mode
        # will remain here for now, but will be refactored in future stories
        # when benchmarking is implemented.
        dummy_benchmark_result = BenchmarkResult(
            n_gpu_layers=0,
            prompt_speed_tps=0.0,
            generation_speed_tps=0.0,
            batch_size=None,
            parallel_level=None
        )

        # For non-interactive mode, we create the configuration with default values.
        # The OutputGenerator will build the complete command from this data.
        notes = []
        if system_profile.cpu_cores == 0:
            notes.append("Could not detect physical CPU cores. The --threads argument was omitted from the command.")

        optimal_config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=dummy_benchmark_result,
            generated_command="",  # Empty - OutputGenerator will build the complete command
            notes=notes,
            ctx_size=2048,  # Default context size for non-interactive
            sampling_parameters={}  # No sampling parameters in non-interactive mode
        )
        
        # For non-interactive mode, we don't need a progress bar, just print the final output

        output_mode = "json" if json_output else ("verbose" if verbose else "default")
        final_output = generate_output(optimal_config, output_mode)

        # All modes output to stdout, but JSON mode should only output JSON
        typer.echo(final_output)

if __name__ == "__main__":
    import asyncio
    asyncio.run(app())
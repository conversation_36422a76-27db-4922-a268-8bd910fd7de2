"""
End-to-end tests for cache functionality.

Tests the cache flags work correctly:
1. --force flag is recognized
2. --no-cache flag is recognized
3. CLI accepts these flags without errors
"""

import pytest
from typer.testing import C<PERSON><PERSON>unner

from llama_tune.cli import app


@pytest.fixture
def runner():
    """CLI test runner."""
    return CliRunner()


class TestCacheE2E:
    """End-to-end tests for cache functionality."""

    def test_force_flag_accepted(self, runner):
        """Test that --force flag is accepted by CLI."""
        # Test that --force flag doesn't cause CLI parsing errors
        # We expect this to fail due to missing model file, but not due to unknown flag
        result = runner.invoke(app, [
            "--benchmark",
            "--model-path", "nonexistent.gguf",
            "--ctx-size", "2048",
            "--force"
        ])

        # Should not be a CLI parsing error (exit code 2)
        # Should be a model file error (exit code 1) or other runtime error
        assert result.exit_code != 2
        assert "Got unexpected extra argument" not in result.stdout

    def test_no_cache_flag_accepted(self, runner):
        """Test that --no-cache flag is accepted by CLI."""
        # Test that --no-cache flag doesn't cause CLI parsing errors
        result = runner.invoke(app, [
            "--benchmark",
            "--model-path", "nonexistent.gguf",
            "--ctx-size", "2048",
            "--no-cache"
        ])

        # Should not be a CLI parsing error (exit code 2)
        assert result.exit_code != 2
        assert "Got unexpected extra argument" not in result.stdout

    def test_both_cache_flags_accepted(self, runner):
        """Test that both --force and --no-cache flags can be used together."""
        result = runner.invoke(app, [
            "--benchmark",
            "--model-path", "nonexistent.gguf",
            "--ctx-size", "2048",
            "--force",
            "--no-cache"
        ])

        # Should not be a CLI parsing error (exit code 2)
        assert result.exit_code != 2
        assert "Got unexpected extra argument" not in result.stdout

    def test_help_shows_cache_flags(self, runner):
        """Test that help output includes the new cache flags."""
        result = runner.invoke(app, ["--help"])

        assert result.exit_code == 0
        assert "--force" in result.stdout
        assert "--no-cache" in result.stdout
        assert "Force a new benchmark" in result.stdout
        assert "bypassing any cached" in result.stdout
        assert "Disable caching of" in result.stdout
        assert "benchmark results" in result.stdout
